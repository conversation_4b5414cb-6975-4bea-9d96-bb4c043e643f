// RTL Support for Projects Dropdown Action Menu
.rtl-layout {
  direction: rtl;
  text-align: right;

  .menu-content {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right !important;
  }

  .menu-link {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;

    i {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  // Reverse icon positioning
  .me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
  }
}

// Arabic dropdown positioning
:host-context(html[lang="ar"]) {
  &.show {
    z-index: 105 !important;
    position: fixed !important;
    margin-right: -80px !important;
    margin-left: auto !important;
  }
}

// Alternative approach for Arabic language
:host-context([dir="rtl"]) {
  &.show {
    z-index: 105 !important;
    position: fixed !important;
    margin-right: -80px !important;
    margin-left: auto !important;
  }
}

// Direct class targeting for Arabic dropdown
.rtl-layout.show,
.show.rtl-layout {
  z-index: 105 !important;
  position: fixed !important;
  margin-right: -80px !important;
  margin-left: auto !important;
}

// Global Arabic dropdown fix
html[lang="ar"] app-projects-dropdown-action-menu.show {
  z-index: 105 !important;
  position: fixed !important;
  margin-right: -80px !important;
  margin-left: auto !important;
}
