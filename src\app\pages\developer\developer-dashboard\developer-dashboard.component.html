<div class="row" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="col-md-3">
    <app-analysis-card [backgroundColor]="'warning'" [title]="getTranslatedText('APARTMENTS')"
      [totalRequests]="numberOfProjects" [activeRequests]="projectStatsData?.apartmentsCount"></app-analysis-card>
    <app-analysis-card [backgroundColor]="'success'" [title]="getTranslatedText('BUILDINGS')"
      [totalRequests]="numberOfProjects" [activeRequests]="projectStatsData?.buildingsCount"></app-analysis-card>
  </div>

  <div class="col-md-3">
    <app-analysis-card [backgroundColor]="'mid-blue'" [title]="getTranslatedText('ADMINISTRATIVE_UNITS')"
      [totalRequests]="numberOfProjects"
      [activeRequests]="projectStatsData?.administrativeUnitsCount"></app-analysis-card>
    <app-analysis-card [backgroundColor]="'dark-blue'" [title]="getTranslatedText('COMMERCIAL_UNITS')"
      [totalRequests]="numberOfProjects" [activeRequests]="projectStatsData?.commercialUnitsCount"></app-analysis-card>
  </div>

  <div class="col-md-3">
    <app-analysis-card [backgroundColor]="'light-dark-blue'" [title]="getTranslatedText('DUPLEX')"
      [totalRequests]="numberOfProjects" [activeRequests]="projectStatsData?.duplexCount"></app-analysis-card>
    <app-analysis-card [backgroundColor]="'danger'" [title]="getTranslatedText('VILLAS')"
      [totalRequests]="numberOfProjects" [activeRequests]="projectStatsData?.villasCount"></app-analysis-card>
  </div>

  <div class="col-md-3">
    <div class="dashboard-pie-chart">
      <div class="pie-chart-section">
        <app-project-pie-chart [newCount]="unitStats?.new" [availableCount]="unitStats?.available"
          [soldCount]="unitStats?.sold" [reservedCount]="unitStats?.reserved">
        </app-project-pie-chart>
      </div>

      <div class="mt-3">
        <app-contract-requests-chart [cssClass]="'mb-5'" [chartSize]="70" [chartLine]="11" [chartRotate]="145"
          [pending]="contractStats?.pending" [accepted]="contractStats?.accepted" [declined]="contractStats?.declined">
        </app-contract-requests-chart>
      </div>
    </div>
  </div>
</div>

<!-- Top 5 Models Section -->
<div class="row mt-5 top-models-section" [class.rtl-section]="translationService.getCurrentLanguage() === 'ar'">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-0 pt-5" [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bolder fs-3 mb-1 text-dark-blue">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#0D47A1" />
              <path d="M19 15L20.09 18.26L24 19L20.09 19.74L19 23L17.91 19.74L14 19L17.91 18.26L19 15Z"
                fill="#2E8BC0" />
              <path d="M5 15L6.09 18.26L10 19L6.09 19.74L5 23L3.91 19.74L0 19L3.91 18.26L5 15Z" fill="#F6C000" />
            </svg>
            {{ getTranslatedText('TOP_5_MODELS') }}
          </span>
          <span class="text-mid-blue mt-1 fw-bold fs-7">
            {{ getTranslatedText('MOST_POPULAR_MODELS') }}
          </span>
        </h3>

        <!-- Date Filters -->
        <div class="card-toolbar" [class.rtl-toolbar]="translationService.getCurrentLanguage() === 'ar'">
          <div class="d-flex align-items-center gap-3"
            [class.rtl-filters]="translationService.getCurrentLanguage() === 'ar'">
            <!-- Date From -->
            <div class="d-flex align-items-center">
              <label class="form-label text-gray-600 fw-semibold fs-7 mb-0"
                [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('DATE_FROM')
                }}:</label>
              <input type="date" class="form-control form-control-sm" [(ngModel)]="dateFrom"
                (change)="onDateFilterChange()" style="width: 140px" />
            </div>

            <!-- Date To -->
            <div class="d-flex align-items-center">
              <label class="form-label text-gray-600 fw-semibold fs-7 mb-0"
                [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('DATE_TO')
                }}:</label>
              <input type="date" class="form-control form-control-sm" [(ngModel)]="dateTo"
                (change)="onDateFilterChange()" style="width: 140px" />
            </div>

            <!-- Clear Filter Button -->
            <button type="button" class="btn btn-sm btn-light-dark-blue" (click)="clearDateFilters()"
              [title]="getTranslatedText('CLEAR_FILTERS')">
              <i class="fa-solid fa-times"></i>
            </button>

            <!-- Apply Filter Button -->
            <button type="button" class="btn btn-sm btn-dark-blue" (click)="applyDateFilters()">
              <i class="fa-solid fa-filter" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
              {{ getTranslatedText('FILTER') }}
            </button>
          </div>
        </div>
      </div>

      <div class="card-body py-3" [class.rtl-body]="translationService.getCurrentLanguage() === 'ar'">
        <div class="row g-3">
          <div class="col-lg-4 col-md-6" *ngFor="let model of filteredTop5Models; let i = index">
            <!-- Simple Model Card -->
            <div class="card h-100 border-0 shadow-sm"
              [class.rtl-card]="translationService.getCurrentLanguage() === 'ar'">
              <!-- Rank Badge -->
              <div class="position-absolute top-0" style="z-index: 1"
                [class.start-0]="translationService.getCurrentLanguage() !== 'ar'"
                [class.end-0]="translationService.getCurrentLanguage() === 'ar'">
                <span class="badge fs-6 fw-bold px-3 py-2" [ngClass]="{
                    'bg-warning text-dark': i === 0,
                    'bg-mid-blue text-white': i === 1,
                    'bg-dark-blue text-white': i === 2,
                    'bg-success text-white': i === 3,
                    'bg-danger text-white': i === 4
                  }"
                  [style.border-radius]="translationService.getCurrentLanguage() === 'ar' ? '0 0 0 8px' : '0 0 8px 0'">
                  #{{ i + 1 }}
                </span>
              </div>

              <div class="card-body p-4 pt-5" [class.rtl-card-body]="translationService.getCurrentLanguage() === 'ar'">
                <!-- Project Name -->
                <h5 class="text-dark-blue fw-bold mb-3 mt-6">
                  {{ model?.projectName || "Unknown Project" }}
                </h5>

                <!-- Model Info -->
                <div class="mb-3">
                  <div class="d-flex justify-content-between mb-2"
                    [class.rtl-info-row]="translationService.getCurrentLanguage() === 'ar'">
                    <span class="text-gray-600 fs-6">{{ getTranslatedText('MODEL_CODE') }}:</span>
                    <span class="text-dark-blue fw-bold fs-6">
                      {{ model?.model?.modelCode || "N/A" }}
                    </span>
                  </div>

                  <div class="d-flex justify-content-between mb-2"
                    [class.rtl-info-row]="translationService.getCurrentLanguage() === 'ar'">
                    <span class="text-gray-600 fs-6">{{ getTranslatedText('MODEL_ID') }}:</span>
                    <span class="text-mid-blue fw-bold fs-6">
                      {{ model?.model?.modelId || "N/A" }}
                    </span>
                  </div>

                  <div class="d-flex justify-content-between"
                    [class.rtl-info-row]="translationService.getCurrentLanguage() === 'ar'">
                    <span class="text-gray-600 fs-6">{{ getTranslatedText('DATE') }}:</span>
                    <span class="text-success fw-bold fs-6">
                      {{ model?.model?.modelDate || "N/A" }}
                    </span>
                  </div>
                </div>

                <!-- View Unit Model Button -->
                <div class="text-center">
                  <a [routerLink]="['/developer/projects/models/units']"
                    [queryParams]="{ modelCode: model?.model?.modelCode }"
                    class="badge badge-light-mid-blue fs-7 fw-semibold px-3 py-2 text-decoration-none"
                    style="cursor: pointer">
                    <i class="fa-solid fa-eye" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                      [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                    {{ getTranslatedText('VIEW_UNIT_MODEL') }}
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="col-12" *ngIf="!filteredTop5Models || filteredTop5Models.length === 0">
            <div class="text-center py-5 empty-state"
              [class.rtl-empty]="translationService.getCurrentLanguage() === 'ar'">
              <div class="symbol symbol-100px mx-auto mb-4">
                <div class="symbol-label bg-light-mid-blue">
                  <i class="fa-solid fa-cube text-mid-blue fs-2x"></i>
                </div>
              </div>
              <h4 class="text-gray-600 fw-bold mb-2">{{ getTranslatedText('NO_MODELS_AVAILABLE') }}</h4>
              <p class="text-gray-500 fs-6">
                {{ getTranslatedText('TOP_MODELS_WILL_APPEAR') }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent contract requests -->
<div class="card mt-5">
  <div class="card-header border-0 pt-5">
    <h3 class="card-title align-items-start flex-column">
      <span class="card-label fw-bolder fs-3 mb-1 text-dark-blue">
        <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_24_2533)">
            <path stroke="#0D47A1" stroke-width="1"
              d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
            <path stroke="#0D47A1" stroke-width="1"
              d="M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z" />
            <path stroke="#0D47A1" stroke-width="1"
              d="M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z" />
          </g>
          <defs>
            <clipPath id="clip0_24_2533">
              <rect width="19" height="19" fill="white" />
            </clipPath>
          </defs>
        </svg>
        {{ getTranslatedText('RECENT_CONTRACT_REQUESTS') }}
      </span>
      <span class="text-danger mt-1 fw-bold fs-7">
        {{ getTranslatedText('YOU_HAVE') }} {{ newRequestsCount }} {{ getTranslatedText('CONTRACT_REQUESTS') }}
      </span>
    </h3>

    <div class="card-toolbar">
      <a [routerLink]="['/developer/projects/requests']" class="btn btn-sm btn-dark-blue btn-active-light-dark-blue">
        {{ getTranslatedText('VIEW_ALL') }}
        <i class="fa-solid fa-angles-right fs-7"></i>
      </a>
    </div>
  </div>

  <div class="card-body py-3">
    <div class="table-responsive">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
        <thead>
          <tr class="fw-bolder bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="min-w-120px ps-4 rounded-start">{{ getTranslatedText('BROKER_NAME') }}</th>
            <th class="min-w-120px">{{ getTranslatedText('MOBILE') }}</th>
            <th class="min-w-120px">{{ getTranslatedText('EMAIL') }}</th>
            <th class="min-w-120px">{{ getTranslatedText('DATE') }}</th>
            <th class="min-w-120px rounded-end">{{ getTranslatedText('STATUS') }}</th>
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let request of newRequests">
            <td>
              <a [routerLink]="" class="text-gray-800 fw-semibold text-hover-primary d-block mb-1 fs-5">
                {{ request?.broker?.fullName || " undefined " }}
              </a>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold fs-5">
                {{ request?.broker?.phone || " undefined " }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold fs-5">
                {{ request?.broker?.email || " undefined " }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-5">
                {{ request?.contract?.createdAt || " undefined " }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-5 fw-semibold" [ngClass]="{
                  'badge-light-warning':
                    request?.contract?.status === 'Pending',
                  'badge-light-success':
                    request?.contract?.status === 'Accepted',
                  'badge-light-danger': request?.contract?.status === 'Rejected'
                }">
                {{ request?.contract?.status || " undefined " }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Chart Section with Modern Design -->
<div class="row g-5 g-xl-8 mb-5 mb-xl-8">
  <div class="col-12">
    <div class="card card-flush h-xl-100">
      <!-- Card Header -->
      <div class="card-header pt-7 pb-3">
        <div class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark fs-1">{{ getTranslatedText('ANALYTICS_CHART') }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ getTranslatedText('CHART_SUBTITLE') }}</span>
        </div>
        <div class="card-toolbar">
          <div class="d-flex align-items-center gap-2">
            <!-- Chart Type Selector -->
            <div class="btn-group" role="group">
              <button type="button" class="btn btn-sm btn-light-primary" [class.active]="barChartType === 'bar'"
                (click)="changeChartType('bar')">
                <i class="fas fa-chart-bar me-1"></i>
                {{ getTranslatedText('BAR_CHART') }}
              </button>
              <button type="button" class="btn btn-sm btn-light-primary" [class.active]="barChartType === 'line'"
                (click)="changeChartType('line')">
                <i class="fas fa-chart-line me-1"></i>
                {{ getTranslatedText('LINE_CHART') }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Card Body -->
      <div class="card-body pt-0 pb-8">
        <div class="chart-container position-relative">
          <canvas baseChart [data]="barChartData" [options]="barChartOptions" [type]="barChartType"
            class="chart-canvas">
          </canvas>
        </div>
      </div>
    </div>
  </div>
</div>