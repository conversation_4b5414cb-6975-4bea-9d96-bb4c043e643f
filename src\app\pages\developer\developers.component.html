<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10 developers-page" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap"
          [class.flex-row-reverse]="translationService.isRTL()">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">
              {{ getTranslatedText('DEVELOPERS') }}
            </h1>
          </div>

          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <i class="fas fa-search fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y"
                [class.ms-3]="!translationService.isRTL()" [class.me-3]="translationService.isRTL()"
                [class.d-none]="translationService.isRTL()"></i>
              <input type="text" name="searchInput" [(ngModel)]="searchInput" (ngModelChange)="onSearch($event)"
                class="form-control form-control-flush bg-light border rounded-pill"
                [class.ps-10]="!translationService.isRTL()" [class.pe-3]="translationService.isRTL()"
                [placeholder]="getTranslatedText('SEARCH_DEVELOPERS')"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'" data-kt-search-element="input" />
            </form>
          </div>

          <div class="d-flex my-4" [class.flex-row-reverse]="translationService.isRTL()">
            <div class="position-relative" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <a class="btn btn-sm btn-light-dark-blue cursor-pointer" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                <i class="fa-solid fa-filter" [class.me-2]="!translationService.isRTL()"
                  [class.ms-2]="translationService.isRTL()"></i>
                {{ getTranslatedText('FILTER') }}
              </a>
            </div>

            <a class="btn btn-sm btn-dark-blue cursor-pointer"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fa-solid fa-plus" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ getTranslatedText('ADD_DEVELOPER') }}
            </a>
          </div>

          <div class="d-flex h-40px my-4">
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap"
              [class.flex-row-reverse]="translationService.isRTL()">
              <li class="nav-item">
                <a [class]="translationService.isRTL() ?
                    'nav-link ms-2 pt-0 pb-0 btn' :
                    'nav-link me-2 pt-0 pb-0 btn'" [class.btn-dark-blue]="activeTab === 'all'"
                  [class.text-white]="activeTab === 'all'" [class.btn-light-dark-blue]="activeTab !== 'all'"
                  routerLink="/developer" (click)="setActiveTab('all')">
                  {{ getTranslatedText('ALL_DEVELOPERS') }}
                </a>
              </li>

              <li class="nav-item">
                <a [class]="translationService.isRTL() ?
                    'nav-link ms-2 pt-0 pb-0 btn' :
                    'nav-link me-2 pt-0 pb-0 btn'" [class.btn-active-dark-blue]="activeTab === 'contracted'"
                  [class.btn-light-dark-blue]="activeTab !== 'contracted'" routerLink="/developer"
                  (click)="setActiveTab('contracted')">
                  {{ getTranslatedText('CONTRACTED') }}
                </a>
              </li>

              <li class="nav-item">
                <a [class]="translationService.isRTL() ?
                    'nav-link ms-2 pt-0 pb-0 btn' :
                    'nav-link me-2 pt-0 pb-0 btn'" [class.btn-active-dark-blue]="activeTab === 'not-contracted'"
                  [class.btn-light-dark-blue]="activeTab !== 'not-contracted'" routerLink="/developer"
                  (click)="setActiveTab('not-contracted')">
                  {{ getTranslatedText('NOT_CONTRACTED') }}
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <router-outlet></router-outlet>

    <div class="table-responsive mb-5" [class.rtl-table]="translationService.isRTL()">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <!-- Checkbox Column - Arabic: First, English: First -->
            <th class="w-25px rounded-start" [class.ps-4]="!translationService.isRTL()"
              [class.pe-4]="translationService.isRTL()" [class.rounded-start]="!translationService.isRTL()"
              [class.rounded-end]="translationService.isRTL()">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>

            <!-- Developer Name Column - Arabic: Second, English: Second -->
            <th class="min-w-150px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('DEVELOPER_NAME') }}
            </th>

            <!-- Email Column - Arabic: Third, English: Third -->
            <th class="min-w-140px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('EMAIL') }}
            </th>

            <!-- Phone Column - Arabic: Fourth, English: Fourth -->
            <th class="min-w-120px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('PHONE') }}
            </th>

            <!-- Number of Projects Column - Arabic: Fifth, English: Fifth -->
            <th class="min-w-150px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('NO_OF_PROJECTS') }}
            </th>

            <!-- Projects Link Column - Arabic: Sixth, English: Sixth -->
            <th class="min-w-100px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('PROJECTS') }}
            </th>

            <!-- Status Column - Arabic: Seventh, English: Seventh -->
            <th class="min-w-100px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('STATUS') }}
            </th>

            <!-- Actions Column - Arabic: Last, English: Last -->
            <th class="min-w-100px rounded-end" [class.text-end]="!translationService.isRTL()"
              [class.text-start]="translationService.isRTL()" [class.pe-4]="!translationService.isRTL()"
              [class.ps-4]="translationService.isRTL()" [class.rounded-end]="!translationService.isRTL()"
              [class.rounded-start]="translationService.isRTL()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ getTranslatedText('ACTIONS') }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <!-- Checkbox Column -->
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>

            <!-- Developer Name Column (Image + Name) -->
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-40px symbol-circle me-3">
                  <img *ngIf="row.image" [src]="row.image" alt="" class="w-10 h-10"
                    style="object-fit: cover; border-radius: 50%" />
                  <div *ngIf="!row.image" class="symbol-label bg-light-primary text-primary fw-bold fs-6">
                    {{ row?.fullName?.charAt(0) }}
                  </div>
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <a class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                    {{ row.fullName }}
                  </a>
                  <span class="text-muted fs-7">{{ row.specialization || 'Developer' }}</span>
                </div>
              </div>
            </td>

            <!-- Email Column -->
            <td>
              <div class="d-flex flex-column">
                <span class="text-gray-800 fw-semibold fs-7 mb-1">{{ row.email }}</span>
                <span class="text-muted fw-semibold fs-7 d-lg-none">{{ row.phone }}</span>
              </div>
            </td>

            <!-- Phone Column -->
            <td class="d-none d-lg-table-cell">
              <span class="text-gray-800 fw-semibold fs-6">{{ row.phone }}</span>
            </td>

            <!-- Number of Projects Column -->
            <td class="d-none d-xl-table-cell">
              <span class="badge badge-light-primary fs-7 fw-bold">{{ row.numberOfProjects }}</span>
            </td>

            <!-- Projects Link Column -->
            <td class="d-none d-lg-table-cell">
              <a [routerLink]="['/developer/projects']" [queryParams]="{ developerId: row.developerId }"
                class="badge badge-light-info fs-7 fw-bold">
                Projects
              </a>
            </td>

            <!-- Status Column -->
            <td>
              <ng-container [ngSwitch]="getDeveloperStatus(row.brokers)">
                <button *ngSwitchCase="getTranslatedText('SEND_CONTRACT_REQUEST')"
                  (click)="openContractModal(contractRequestModal, row)" class="btn btn-sm btn-primary"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ getTranslatedText('SEND_CONTRACT_REQUEST') }}
                </button>

                <span *ngSwitchDefault class="badge fs-7 fw-bold px-3 py-2 badge-light-success"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ getTranslatedText('CONTRACTED') }}
                </span>
              </ng-container>
            </td>

            <!-- Actions Column -->
            <td [class.text-end]="!translationService.isRTL()" [class.text-start]="translationService.isRTL()"
              [class.pe-4]="!translationService.isRTL()" [class.ps-4]="translationService.isRTL()">
              <a [routerLink]="['/developer/projects']" [queryParams]="{ developerId: row.developerId }"
                class="btn btn-sm btn-icon btn-light-primary" data-bs-toggle="tooltip"
                [title]="getTranslatedText('VIEW_DEVELOPER')">
                <i class="fas fa-eye fs-6"></i>
              </a>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div *ngIf="!loading && rows.length > 0" class="d-flex justify-content-center mt-5 mb-5">
        <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
          (pageChange)="onPageChange($event)"></app-pagination>
      </div>
    </div>
  </div>
</div>

<router-outlet></router-outlet>

<!-- Contract Request Modal -->
<ng-template #contractRequestModal let-modal>
  <div class="modal-header" [class.flex-row-reverse]="translationService.isRTL()">
    <h4 class="modal-title" id="modal-basic-title">{{ getTranslatedText('SEND_CONTRACT_REQUEST_MODAL') }}</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('closed')"></button>
  </div>
  <div class="modal-body" [class.text-end]="translationService.isRTL()">
    <div class="row">
      <div class="">
        <!-- Document Upload Section -->
        <div class="container p-4 bg-white rounded shadow-sm">
          <!-- Header -->
          <div class="text-center mb-4">
            <h5 class="fw-bold text-primary" style="color: #2e2a7e !important">
              {{ getTranslatedText('UPLOAD_DOCUMENTS') }}
            </h5>
            <p class="text-success mt-2" style="font-size: 12px">
              {{ getTranslatedText('UPLOAD_DOCUMENTS_DESC') }}
            </p>
          </div>

          <!-- Document Upload Cards -->
          <div class="mb-4">
            <!-- Personal Photo -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="image" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    {{ getTranslatedText('PROFILE_PICTURE') }}

                    <span *ngIf="getFileCount('image') > 0"
                      [class]="translationService.isRTL() ? 'badge bg-success me-2' : 'badge bg-success ms-2'">
                      {{ getFileCount("image") }}
                    </span>
                  </p>

                  <p class="text-muted small mb-0">
                    <span>{{ getTranslatedText('FILE_SIZE') }}</span>
                  </p>
                </div>
                <input type="file" id="image" class="d-none" (change)="onFileChange($event, 'image')"
                  accept="image/*" />
              </label>
            </div>

            <!-- ID Front -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="idFront" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    {{ getTranslatedText('NATIONAL_ID_FRONT') }}
                    <span *ngIf="getFileCount('idFront') > 0"
                      [class]="translationService.isRTL() ? 'badge bg-success me-2' : 'badge bg-success ms-2'">
                      {{ getFileCount("idFront") }}
                    </span>
                  </p>
                  <p class="text-muted small mb-0">
                    <span>{{ getTranslatedText('FILE_SIZE') }}</span>
                  </p>
                </div>
                <input type="file" id="idFront" class="d-none" (change)="onFileChange($event, 'idFront')"
                  accept="image/*" />
              </label>
            </div>

            <!-- ID Back -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="idBack" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    {{ getTranslatedText('NATIONAL_ID_BACK') }}
                    <span *ngIf="getFileCount('idBack') > 0"
                      [class]="translationService.isRTL() ? 'badge bg-success me-2' : 'badge bg-success ms-2'">
                      {{ getFileCount("idBack") }}
                    </span>
                  </p>
                  <p class="text-muted small mb-0">
                    <span>{{ getTranslatedText('FILE_SIZE') }}</span>
                  </p>
                </div>
                <input type="file" id="idBack" class="d-none" (change)="onFileChange($event, 'idBack')"
                  accept="image/*" />
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer" [class.flex-row-reverse]="translationService.isRTL()">
    <button type="button" class="btn btn-light" (click)="modal.dismiss('Cancel')">
      {{ getTranslatedText('CANCEL') }}
    </button>
    <button type="button" class="btn btn-dark-blue" [disabled]="isSendingRequest || !areAllFilesUploaded()"
      (click)="sendContractRequest()">
      {{ getTranslatedText('SEND_REQUEST') }}
      <span *ngIf="isSendingRequest"
        [class]="translationService.isRTL() ? 'spinner-border spinner-border-sm align-middle me-2' : 'spinner-border spinner-border-sm align-middle ms-2'"></span>
    </button>
  </div>
</ng-template>