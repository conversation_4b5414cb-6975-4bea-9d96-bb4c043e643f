import { Component, OnInit } from '@angular/core';
import { AuthenticationService } from 'src/app/pages/authentication';
import { ChatService } from 'src/app/pages/shared/services/chat.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-sidebar-menu',
  templateUrl: './sidebar-menu.component.html',
  styleUrls: ['./sidebar-menu.component.scss'],
})
export class SidebarMenuComponent implements OnInit {

  // user: any = { role: 'client' };
  // user: any = { role: 'broker' };
  // user: any = { role: 'developer' };

  user: any;

  messageNotificationCounter = 0;

  constructor(
    private chatService: ChatService,
    private authenticationService: AuthenticationService,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.user = this.authenticationService.getSessionUser();
    console.log(this.user);
    this.chatService.counter$.subscribe(count => {
    this.messageNotificationCounter = count;
    });
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'PROJECT': 'مشروع',
        'VIEW_ALL_PROJECTS': 'عرض جميع المشاريع',
        'CREATE_PROJECT': 'إنشاء مشروع',
        'CONTRACT_REQUESTS': 'طلبات العقود'
      },
      'en': {
        'PROJECT': 'Project',
        'VIEW_ALL_PROJECTS': 'View all projects',
        'CREATE_PROJECT': 'Create project',
        'CONTRACT_REQUESTS': 'Contract Requests'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Function to reload page when navigating to profile
  navigateToProfile(): void {
    window.location.href = '/profile';
  }
}
