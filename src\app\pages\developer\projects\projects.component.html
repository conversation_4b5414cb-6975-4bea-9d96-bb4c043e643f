<div class="mb-5 mt-0">
  <app-broker-title *ngIf="user?.role === 'broker'"></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-4"
          [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 page-title-custom">{{ getTranslatedText('PROJECTS') }}</h1>
          </div>
          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="position-relative" autocomplete="off">
              <app-keenicon name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y"
                [class.ms-3]="translationService.getCurrentLanguage() !== 'ar'"
                [class.me-3]="translationService.getCurrentLanguage() === 'ar'"
                [class.d-none]="translationService.getCurrentLanguage() === 'ar'" type="outline"></app-keenicon>
              <input type="text" name="searchText" class="form-control form-control-flush bg-light border rounded-pill"
                [class.ps-10]="translationService.getCurrentLanguage() !== 'ar'"
                [class.pe-3]="translationService.getCurrentLanguage() === 'ar'" [(ngModel)]="searchText"
                (ngModelChange)="onSearchTextChange($event)" [placeholder]="getTranslatedText('SEARCH_BY_PROJECT_NAME')"
                data-kt-search-element="input" style="min-width: 280px;" />
            </form>
          </div>
          <div class="d-flex my-4 gap-3">
            <div class="position-relative">
              <a class="btn btn-sm btn-light-dark-blue cursor-pointer" (click)="toggleFilterDropdown()">
                <i class="fa-solid fa-filter" [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
                {{ getTranslatedText('FILTER') }}
              </a>

              <!-- Filter Dropdown -->
              <div *ngIf="isFilterDropdownVisible" class="dropdown-menu show p-4 shadow"
                style="position: absolute; top: 100%; z-index: 1000; min-width: 200px; width: auto;"
                [style.right]="translationService.getCurrentLanguage() !== 'ar' ? '0px' : 'auto'"
                [style.left]="translationService.getCurrentLanguage() === 'ar' ? '0px' : 'auto'">
                <app-project-filter (filtersApplied)="onFiltersApplied($event)"></app-project-filter>
              </div>
            </div>

            <a *ngIf="user?.role === 'developer'" [routerLink]="['/developer/projects/create']"
              class="btn btn-sm btn-dark-blue cursor-pointer">
              <i class="fa-solid fa-plus" [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
              {{ getTranslatedText('CREATE_PROJECT') }}
            </a>
          </div>
        </div>
      </div>
    </div>

    <app-empty-properties-card *ngIf="showEmptyCard && user?.role =='developer'"
      [userRole]="user?.role"></app-empty-properties-card>

    <!-- Broker empty state message -->
    <div *ngIf="showEmptyCard && user?.role === 'broker'" class="text-center py-5">
      <div class="card border-0 shadow-sm">
        <div class="card-body py-5">
          <i class="fa-solid fa-building-circle-exclamation text-muted fs-1 mb-4"></i>
          <h4 class="text-muted fw-bold mb-3">{{ getTranslatedText('NO_PROJECTS_FOUND') }}</h4>
          <p class="text-muted fs-6 mb-0">{{ getTranslatedText('NO_PROJECTS_FOR_BROKER') }}</p>
        </div>
      </div>
    </div>

    <div class="table-responsive mb-5" *ngIf="!showEmptyCard">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('name')">
              {{ getTranslatedText('PROJECT') }}
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("name")
                }}</span>
            </th>
            <!-- <th class="min-w-100px">
              Type
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th> -->
            <th class="min-w-100px cursor-pointer" (click)="sortData('city_id')">
              {{ getTranslatedText('AREA') }}
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("city_id")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('buildings_count')">
              {{ getTranslatedText('NO_OF_BUILDINGS') }}
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("buildings_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('apartments_count')">
              {{ getTranslatedText('NO_OF_APARTMENTS') }}
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("apartments_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('villas_count')">
              {{ getTranslatedText('NO_OF_VILLAS') }}
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("villas_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('duplex_count')">
              {{ getTranslatedText('NO_OF_DUPLEX') }}
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("duplex_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('commercial_units_count')">
              {{ getTranslatedText('PROPERTIES') }}
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("commercial_units_count")
                }}</span>
            </th>

            <th>
              Action
            </th>

          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-45px me-5">
                  <img [src]="row.logoImage" alt="img" class="rounded-circle" />
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <!-- [queryParams]="{ id: row.id }" -->
                  <a [routerLink]="['/developer/projects/models']" [queryParams]="{ projectId: row.id }"
                    class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                    {{ row.name }}
                  </a>
                  <span class="text-muted fs-7">{{ row.area.name_en }}</span>
                </div>
              </div>
            </td>
            <!-- <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.projectType }}
              </span>
            </td> -->
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.city.name_en }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.buildingsCount }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.apartmentsCount }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.villasCount }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.duplexCount }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ getTranslatedText('COMMERCIAL') }}: {{ row.commercialUnitsCount }}<br />
                {{ getTranslatedText('ADMINISTRATIVE') }}: {{ row.administrativeUnitsCount }}
              </span>
            </td>
            <!-- <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.brokers }}
              </span>
            </td> -->
            <td class="text-end pe-4" *ngIf="user?.role === 'developer'">
              <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                <i class="fa-solid fa-ellipsis-vertical"></i>
              </button>
              <app-projects-dropdown-action-menu [id]="row.id"></app-projects-dropdown-action-menu>
            </td>

          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <!-- Pagination -->
  <div *ngIf="!loading && rows.length > 0" class="d-flex justify-content-center mt-5 mb-5">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>

<router-outlet></router-outlet>