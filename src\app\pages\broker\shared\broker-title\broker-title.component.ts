import { BrokerService } from './../../services/broker.service';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { AccountTypeMapper } from '../../account-type-mapper';
import { AuthenticationService } from 'src/app/pages/authentication';
import { PermissionService } from 'src/app/pages/shared/services/permission.service';
import { TranslationService } from 'src/app/modules/i18n';
import { TranslateService } from '@ngx-translate/core';
import { PropertyTranslationService } from 'src/app/shared/services/property-translation.service';

@Component({
  selector: 'app-broker-title',
  templateUrl: './broker-title.component.html',
  styleUrl: './broker-title.component.scss',
})

export class BrokerTitleComponent implements OnInit {
  @Input() showCreateButton: boolean = true;
  user: any = {};
  currentLang: string = 'en';

  constructor(
    protected cd: ChangeDetectorRef,
    private brokerService: BrokerService,
    private authenticationService:AuthenticationService,
    public permissionService:PermissionService,
    private translationService: TranslationService,
    private translateService: TranslateService,
    private propertyTranslationService: PropertyTranslationService
  ) {}

  ngOnInit(): void {
    // let currentUser = localStorage.getItem('currentUser');
    // let userId = currentUser ? JSON.parse(currentUser).id : null;
    // this.getUser(userId);
    this.user = this.authenticationService.getSessionUser();

    // Initialize current language
    this.currentLang = this.translationService.getCurrentLanguage();

    // Subscribe to language changes
    this.translationService.currentLanguage$.subscribe(lang => {
      this.currentLang = lang;
      this.cd.detectChanges();
    });
  }

  getUser(id: any) {
    this.brokerService.getById(id).subscribe({
      next: (response: any) => {
        console.log(response);
        this.user = response?.data ?? {};
        this.cd.detectChanges();
      },
      error: (error: any) => {
        Swal.fire(error.error.message, '', 'error');
      },
    });
  }

  capitalizeWords(text: string | null): string {
    if (!text) return '';
    return text.replace(/\b\w/g, (char) => char.toUpperCase());
  }

  getAccountTypeBadge(type: string): string {
    return AccountTypeMapper.getAccountTypeBadge(type);
  }

  hasPermission(permission: string){
    console.log(this.permissionService.hasPermission(permission));
    return this.permissionService.hasPermission(permission);
  }

  getTranslatedAccountType(accountType: string): string {
    if (!accountType) return '';

    // Create translation key from account type
    const translationKey = `ACCOUNT_TYPES.${accountType.toUpperCase().replace(/\s+/g, '_')}`;

    // Get translation, fallback to capitalized original if not found
    const translation = this.translateService.instant(translationKey);

    // If translation key is returned as-is, it means no translation was found
    if (translation === translationKey) {
      return this.capitalizeWords(accountType);
    }

    return translation;
  }

  // Translate specialization using PropertyTranslationService
  getTranslatedSpecialization(specialization: string): string {
    if (!specialization) return '';

    const currentLang = this.currentLang as 'en' | 'ar';
    return this.propertyTranslationService.translatePropertyType(specialization, currentLang, 50);
  }
}
