.modern-header {
  position: relative;
  background: linear-gradient(135deg, #0d47a1 0%, #1565c0 50%, #1976d2 100%);
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(13, 71, 161, 0.25);
  min-height: 100px;
  border: 1px solid rgba(13, 71, 161, 0.1);
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.header-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 50%, white 2px, transparent 2px),
    radial-gradient(circle at 80% 50%, white 2px, transparent 2px);
  background-size: 30px 30px;
  animation: float 6s ease-in-out infinite;
}

.header-content {
  position: relative;
  padding: 2rem 2.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  i {
    font-size: 1.5rem;
    color: white;
  }
}

.header-text {
  color: white;
}

.header-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.header-subtitle {
  font-size: 0.9rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 400;
}

.header-decoration {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.decoration-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;

  &.circle-1 {
    animation-delay: 0s;
  }

  &.circle-2 {
    animation-delay: 0.5s;
    width: 8px;
    height: 8px;
  }

  &.circle-3 {
    animation-delay: 1s;
    width: 6px;
    height: 6px;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .modern-header {
    border-radius: 15px;
    min-height: 100px;
  }

  .header-content {
    padding: 1.5rem 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .header-main {
    flex-direction: column;
    gap: 1rem;
  }

  .header-title {
    font-size: 1.5rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
  }

  .header-decoration {
    justify-content: center;
  }
}

// RTL Support
:host-context(html[lang="ar"]) {
  .header-main {
    flex-direction: row-reverse;
  }

  .header-content {
    text-align: right;
  }

  .header-title,
  .header-subtitle {
    font-family: 'Cairo', sans-serif;
  }
}

@media (max-width: 768px) {
  .header-right {
    display: none;
  }

  .header-center {
    text-align: right;
  }
}
