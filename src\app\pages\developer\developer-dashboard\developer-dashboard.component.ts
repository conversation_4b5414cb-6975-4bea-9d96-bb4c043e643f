import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { DevelopersService } from '../services/developer.service';
import { getCSSVariableValue } from 'src/app/_metronic/kt/_utils';
import { TranslationService } from 'src/app/modules/i18n';
import { ChartConfiguration, ChartData, ChartOptions, ChartType } from 'chart.js';

@Component({
  selector: 'app-developer-dashboard',
  templateUrl: './developer-dashboard.component.html',
  styleUrl: './developer-dashboard.component.scss',
})
export class DeveloperDashboardComponent implements OnInit {

  newRequests: any[] = [];
  newRequestsCount = 0;
  unitStats: any = {};
  projectStats: any = {};
  contractStats: any = {};
  projectStatsData: any = {
    apartments_count: 0,
    buildings_count: 0,
    villas_count: 0,
    duplex_count: 0,
    administrative_units_count: 0,
    commercial_units_count: 0,
  };
  numberOfProjects: number = 0;
  developerId: number;

  // Top 5 Models data
  top5ModelsOverAll: any[] = [];
  filteredTop5Models: any[] = [];

public barChartOptions: ChartConfiguration<'bar'>['options'] = {
    responsive: true,
    scales: {
      x: {
        type: 'category'
      },
      y: {
        beginAtZero: true,
        min: 0,
        max: 20,
        ticks: {
          stepSize: 5
        },
        title: {
          display: true,
          text: 'Y Axis'
        }
      }
    }
  };

  barChartType: any = 'bar';
  public barChartLabels: string[] = [];
  barChartData: any = {
    labels: [],
    datasets: [],
  };

  // Date filters
  dateFrom: string = '';
  dateTo: string = '';

  orderBy: string = 'id';
  orderDir: 'asc' | 'desc' = 'desc';

  constructor(
    private developerService: DevelopersService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.developerId = user?.developerId;
    this.loadStatistics();
    this.loadPieChartStatistics();
    this.loadBarChartStatistics();
  }

  loadStatistics() {
    this.developerService.getDeveloperStatistics(this.developerId).subscribe({
      next: (response) => {
        console.log('Statistics response:', response.data);
        console.log('Statistics count:', response.count);
        this.newRequests = response.data;
        this.newRequestsCount = response.count;

        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading statistics:', error);
        this.newRequests = [];
      },
    });
  }

  loadPieChartStatistics() {
    this.developerService
      .getDeveloperPieChartStatistics(this.developerId)
      .subscribe({
        next: (response) => {
          if (response.data) {
            const data = response.data;
            this.unitStats = data.statistics.unitStats;
            this.contractStats = data.statistics.contractRequestStats;
            this.projectStatsData = data.statistics.projectStats;
            this.numberOfProjects = data.statistics.numberOfProjects;
            this.top5ModelsOverAll = data.top5ModelsOverAll;
            this.filteredTop5Models = [...this.top5ModelsOverAll];
            this.cd.detectChanges();
          }
        },
        error: (error) => {
          console.error('Error loading pie chart statistics:', error);
        },
      });
  }

  loadBarChartStatistics() {
  this.developerService
    .getDeveloperBarChartStatistics(this.developerId)
    .subscribe({
      next: (response) => {
        console.log(response);

        // Assign the response to projectStats
        this.projectStats = response.data;

        // Map data for bar chart
        this.barChartLabels = this.projectStats.map((p:any) => p.projectName);
        this.barChartData = {
          labels: this.projectStats.map((p:any) => p.projectName),
          datasets: [
            {
              data: this.projectStats.map((p:any) => p.soldUnitsCount),
              label: 'Sold Units'
            }
          ],
        };
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading bar chart statistics:', error);
      },
    });
  }

  sortData(column: string) {
    this.orderDir =
      this.orderBy === column
        ? this.orderDir === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
    this.orderBy = column;

    this.newRequests.sort((a, b) => {
      const valA = column.includes('.')
        ? column.split('.').reduce((o, k) => o?.[k], a)
        : a[column];
      const valB = column.includes('.')
        ? column.split('.').reduce((o, k) => o?.[k], b)
        : b[column];
      return this.orderDir === 'asc'
        ? valA > valB
          ? 1
          : -1
        : valA < valB
        ? 1
        : -1;
    });
  }

  getSortArrow(column: string): string {
    return this.orderBy === column ? (this.orderDir === 'asc' ? '↑' : '↓') : '';
  }

  onDateFilterChange() {
    this.applyDateFilters();
  }

  applyDateFilters() {
    if (!this.dateFrom && !this.dateTo) {
      this.filteredTop5Models = [...this.top5ModelsOverAll];
      return;
    }

    this.filteredTop5Models = this.top5ModelsOverAll.filter((model) => {
      const modelDate = new Date(model?.model?.modelDate);

      if (this.dateFrom && this.dateTo) {
        const fromDate = new Date(this.dateFrom);
        const toDate = new Date(this.dateTo);
        return modelDate >= fromDate && modelDate <= toDate;
      } else if (this.dateFrom) {
        const fromDate = new Date(this.dateFrom);
        return modelDate >= fromDate;
      } else if (this.dateTo) {
        const toDate = new Date(this.dateTo);
        return modelDate <= toDate;
      }

      return true;
    });
  }

  clearDateFilters() {
    this.dateFrom = '';
    this.dateTo = '';
    this.filteredTop5Models = [...this.top5ModelsOverAll];
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'DASHBOARD': 'لوحة التحكم',
        'DEVELOPER_DASHBOARD': 'لوحة تحكم المطور',
        'STATISTICS': 'الإحصائيات',
        'NEW_REQUESTS': 'الطلبات الجديدة',
        'TOTAL_UNITS': 'إجمالي الوحدات',
        'TOTAL_PROJECTS': 'إجمالي المشاريع',
        'CONTRACT_REQUESTS': 'طلبات العقود',
        'UNIT_STATISTICS': 'إحصائيات الوحدات',
        'PROJECT_STATISTICS': 'إحصائيات المشاريع',
        'TOP_5_MODELS': 'أفضل 5 نماذج',
        'APARTMENTS': 'شقق',
        'VILLAS': 'فيلات',
        'DUPLEX': 'دوبلكس',
        'BUILDINGS': 'مباني',
        'COMMERCIAL_UNITS': 'وحدات تجارية',
        'ADMINISTRATIVE_UNITS': 'وحدات إدارية',
        'DATE_FROM': 'من تاريخ',
        'DATE_TO': 'إلى تاريخ',
        'FILTER': 'تصفية',
        'CLEAR_FILTERS': 'مسح المرشحات',
        'NO_DATA_AVAILABLE': 'لا توجد بيانات متاحة',
        'LOADING': 'جاري التحميل...',
        'ERROR_LOADING_DATA': 'خطأ في تحميل البيانات',
        'MOST_POPULAR_MODELS': 'النماذج الأكثر شعبية عبر جميع المشاريع',
        'MODEL_CODE': 'كود النموذج',
        'MODEL_ID': 'معرف النموذج',
        'DATE': 'التاريخ',
        'VIEW_UNIT_MODEL': 'عرض نموذج الوحدة',
        'NO_MODELS_AVAILABLE': 'لا توجد نماذج متاحة',
        'TOP_MODELS_WILL_APPEAR': 'ستظهر أفضل النماذج هنا عند توفر البيانات',
        'RECENT_CONTRACT_REQUESTS': 'طلبات العقود الحديثة',
        'YOU_HAVE': 'لديك',
        'VIEW_ALL': 'عرض الكل',
        'BROKER_NAME': 'اسم الوسيط',
        'MOBILE': 'الجوال',
        'EMAIL': 'البريد الإلكتروني',
        'STATUS': 'الحالة',
        'ANALYTICS_CHART': 'مخطط التحليلات',
        'CHART_SUBTITLE': 'إحصائيات المشاريع والوحدات',
        'BAR_CHART': 'مخطط أعمدة',
        'LINE_CHART': 'مخطط خطي'
      },
      'en': {
        'DASHBOARD': 'Dashboard',
        'DEVELOPER_DASHBOARD': 'Developer Dashboard',
        'STATISTICS': 'Statistics',
        'NEW_REQUESTS': 'New Requests',
        'TOTAL_UNITS': 'Total Units',
        'TOTAL_PROJECTS': 'Total Projects',
        'CONTRACT_REQUESTS': 'Contract Requests',
        'UNIT_STATISTICS': 'Unit Statistics',
        'PROJECT_STATISTICS': 'Project Statistics',
        'TOP_5_MODELS': 'Top 5 Models',
        'APARTMENTS': 'Apartments',
        'VILLAS': 'Villas',
        'DUPLEX': 'Duplex',
        'BUILDINGS': 'Buildings',
        'COMMERCIAL_UNITS': 'Commercial Units',
        'ADMINISTRATIVE_UNITS': 'Administrative Units',
        'DATE_FROM': 'Date From',
        'DATE_TO': 'Date To',
        'FILTER': 'Filter',
        'CLEAR_FILTERS': 'Clear Filters',
        'NO_DATA_AVAILABLE': 'No data available',
        'LOADING': 'Loading...',
        'ERROR_LOADING_DATA': 'Error loading data',
        'MOST_POPULAR_MODELS': 'Most popular models across all projects',
        'MODEL_CODE': 'Model Code',
        'MODEL_ID': 'Model ID',
        'DATE': 'Date',
        'VIEW_UNIT_MODEL': 'View Unit Model',
        'NO_MODELS_AVAILABLE': 'No Models Available',
        'TOP_MODELS_WILL_APPEAR': 'Top models will appear here once data is available',
        'RECENT_CONTRACT_REQUESTS': 'Recent Contract Requests',
        'YOU_HAVE': 'You have',
        'VIEW_ALL': 'View all',
        'BROKER_NAME': 'Broker Name',
        'MOBILE': 'Mobile',
        'EMAIL': 'Email',
        'STATUS': 'Status',
        'ANALYTICS_CHART': 'Analytics Chart',
        'CHART_SUBTITLE': 'Projects and Units Statistics',
        'BAR_CHART': 'Bar Chart',
        'LINE_CHART': 'Line Chart'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Change chart type function
  changeChartType(type: string): void {
    this.barChartType = type;
    this.cd.detectChanges();
  }
}
