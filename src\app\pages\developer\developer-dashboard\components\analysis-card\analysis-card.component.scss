// RTL Support for Analysis Card - إصلاح شامل
.rtl-layout {
  direction: rtl;
  text-align: right;

  .fs-7, .fs-4 {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .fw-bold, .fw-bolder {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .col {
    text-align: right;
  }

  .d-flex.align-items-center {
    justify-content: flex-start; // تصحيح المحاذاة
    flex-direction: row; // ترتيب طبيعي
  }

  // إصلاح الـ badge
  .card-header {
    position: relative;
    text-align: right;

    .badge {
      font-family: 'Hacen Liner Screen St', sans-serif;
      position: absolute;
      right: 1rem;
      top: 1rem;
      margin: 0 !important;
    }
  }
}

// Enhanced styling for Arabic - مبسط ومصحح
:host-context(html[lang="ar"]) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .card-header {
    position: relative;
    text-align: right;
    padding: 1.5rem 1rem;

    .badge {
      font-family: 'Hacen Liner Screen St', sans-serif !important;
      font-size: 0.9rem !important;
      font-weight: 600 !important;
      position: absolute;
      right: 1rem;
      top: 1rem;
      margin: 20px !important;
    }
  }

  .card-body {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;

    .fs-7 {
      font-size: 0.9rem !important;
      font-weight: 600;
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .fs-4 {
      font-size: 1.4rem !important;
      font-weight: 700;
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }
}

// كلاس مساعد للـ badge في العربية - مبسط
.rtl-badge {
  font-family: 'Hacen Liner Screen St', sans-serif !important;
  position: absolute !important;
  right: 1rem !important;
  top: 1rem !important;
  margin: 0 !important;
}

// كلاس للـ header في العربية
.rtl-header {
  direction: rtl;
  text-align: right;

  .card-title {
    justify-content: flex-end;

    .badge {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
      margin: 0;
    }
  }
}

// إصلاح إضافي للتصميم
.card {
  overflow: visible;
  position: relative;

  .card-header {
    min-height: 4rem;
    position: relative;

    &.rtl-header {
      .badge {
        z-index: 10;
        white-space: nowrap;
      }
    }
  }
}

// تحسين عام للعربية
:host-context(html[lang="ar"]) {
  .card {
    .badge {
      font-family: 'Hacen Liner Screen St', sans-serif !important;
      font-size: 0.85rem !important;
      padding: 0.4rem 0.8rem !important;
      border-radius: 0.375rem !important;
    }
  }
}
