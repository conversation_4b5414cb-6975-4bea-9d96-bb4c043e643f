import { ChangeDetectorRef, Component, EventEmitter, Output, Input, OnInit } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';
import { TranslationService } from 'src/app/modules/i18n';
import { PropertyTranslationService } from 'src/app/shared/services/property-translation.service';

@Component({
  selector: 'app-unit-filter',
  templateUrl: './unit-filter.component.html',
  styleUrl: './unit-filter.component.scss'
})
export class UnitFilterComponent implements OnInit {

  unitTypes: { key: string; value: string }[] = [];
  areas: any[] = [];

  @Input() brokerId: number | null = null;
  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    finishingType: '',
    status: '',
    unitType: '',
    unitArea:'',
    area:'',
    view:'',
    price:'',
  };

  constructor(
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    public translationService: TranslationService,
    private propertyTranslationService: PropertyTranslationService
  ) {}

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'AREA': 'المنطقة',
        'SELECT': 'اختر',
        'UNIT_AREA': 'مساحة الوحدة',
        'ENTER_UNIT_AREA': 'أدخل مساحة الوحدة',
        'VIEW': 'الإطلالة',
        'UNIT_TYPE': 'نوع الوحدة',
        'SELECT_UNIT_TYPE': 'اختر نوع الوحدة',
        'PRICE': 'السعر',
        'ENTER_PRICE': 'أدخل السعر',
        'APPLY': 'تطبيق',
        'RESET': 'إعادة تعيين'
      },
      'en': {
        'AREA': 'Area',
        'SELECT': 'Select',
        'UNIT_AREA': 'Unit Area',
        'ENTER_UNIT_AREA': 'Enter unit area',
        'VIEW': 'View',
        'UNIT_TYPE': 'Unit Type',
        'SELECT_UNIT_TYPE': 'Select Unit Type',
        'PRICE': 'Price',
        'ENTER_PRICE': 'Enter price',
        'APPLY': 'Apply',
        'RESET': 'Reset'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  ngOnInit(): void {
    this.loadUnitTypes();
    this.loadAreas();
  }

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

  status: { key: string; value: string }[] = [
    { key: 'NEW', value: 'new' },
    { key: 'AVAILABLE', value: 'available' },
    { key: 'RESERVED', value: 'reserved' },
    { key: 'SOLD', value: 'sold' },
  ];

  get views(): { key: string; value: string }[] {
    const currentLang = this.translationService.getCurrentLanguage();
    const viewTranslations: any = {
      'ar': {
        'water_view': 'إطلالة مائية',
        'gardens_and_landscape': 'حدائق ومناظر طبيعية',
        'street': 'شارع',
        'entertainment_area': 'منطقة ترفيهية',
        'garden': 'حديقة',
        'main_street': 'شارع رئيسي',
        'square': 'ميدان',
        'side_street': 'شارع جانبي',
        'rear_view': 'إطلالة خلفية'
      },
      'en': {
        'water_view': 'Water View',
        'gardens_and_landscape': 'Gardens and Landscape',
        'street': 'Street',
        'entertainment_area': 'Entertainment Area',
        'garden': 'Garden',
        'main_street': 'Main Street',
        'square': 'Square',
        'side_street': 'Side Street',
        'rear_view': 'Rear View'
      }
    };

    return [
      { key: viewTranslations[currentLang]?.['water_view'] || 'Water View', value: 'water_view' },
      { key: viewTranslations[currentLang]?.['gardens_and_landscape'] || 'Gardens and Landscape', value: 'gardens_and_landscape' },
      { key: viewTranslations[currentLang]?.['street'] || 'Street', value: 'street' },
      { key: viewTranslations[currentLang]?.['entertainment_area'] || 'Entertainment Area', value: 'entertainment_area' },
      { key: viewTranslations[currentLang]?.['garden'] || 'Garden', value: 'garden' },
      { key: viewTranslations[currentLang]?.['main_street'] || 'Main Street', value: 'main_street' },
      { key: viewTranslations[currentLang]?.['square'] || 'Square', value: 'square' },
      { key: viewTranslations[currentLang]?.['side_street'] || 'Side Street', value: 'side_street' },
      { key: viewTranslations[currentLang]?.['rear_view'] || 'Rear View', value: 'rear_view' },
    ];
  }

  apply() {
    const filtersWithBrokerId = {
      ...this.filter,
      brokerId: this.brokerId
    };
    console.log('Unit filter applying:', filtersWithBrokerId);
    this.filtersApplied.emit(filtersWithBrokerId);
  }

  reset() {
    this.filter = {
      finishingType: '',
      status: '',
      unitType: '',
      unitArea:'',
      area:'',
      view:'',
      price:'',
    };

    const resetFiltersWithBrokerId = {
      ...this.filter,
      brokerId: this.brokerId
    };
    console.log('Unit filter resetting:', resetFiltersWithBrokerId);
    this.filtersApplied.emit(resetFiltersWithBrokerId);
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        const currentLang = this.translationService.getCurrentLanguage() as 'en' | 'ar';

        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key: this.propertyTranslationService.translatePropertyType(key, currentLang, 50),
          value: value as string,
        }));
        console.log('Translated Unit Types:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

   loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }
}
