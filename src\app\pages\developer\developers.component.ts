import { ChangeDetectorRef, Component } from '@angular/core';
import { BaseGridComponent } from '../shared/base-grid/base-grid.component';
import { DevelopersService } from '../broker/services/developers.service';
import { ContractService } from './services/contract.service';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import Swal from 'sweetalert2';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-developers',
  templateUrl: './developers.component.html',
  styleUrl: './developers.component.scss',
})
export class DevelopersComponent extends BaseGridComponent {
  activeTab: 'all' | 'contracted' | 'not-contracted' = 'all';
  selectedDeveloper: any = null;
  searchInput: string = '';
  private searchSubject = new Subject<string>();
  brokerId: number ;

  stepForm: FormGroup;

  // Loading state
  isSendingRequest = false;

  constructor(
    protected cd: ChangeDetectorRef,
    private developersService: DevelopersService,
    private contractService: ContractService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    public translationService: TranslationService
  ) {
    super(cd);
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = user?.brokerId;
    this.setService(developersService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.initForms();
  }

  initForms() {
    this.stepForm = this.fb.group({
      image: [[]],
      idFront: [[]],
      idBack: [[]],
    });
  }

  onFileChange(event: any, fileType: string) {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);

      this.stepForm.patchValue({
        [fileType]: fileArray,
      });
    }
  }

  getFileCount(fieldName: string): number {
    const files = this.stepForm.get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  areAllFilesUploaded(): boolean {
    const imageCount = this.getFileCount('image');
    const idFrontCount = this.getFileCount('idFront');
    const idBackCount = this.getFileCount('idBack');

    return imageCount > 0 && idFrontCount > 0 && idBackCount > 0;
  }

  openContractModal(content: any, developer?: any) {
    if (developer) {
      this.selectedDeveloper = developer;
      console.log('Developer ID:', developer.developerId);
    }

    this.modalService.open(content, {
      centered: true,
      size: 'md',
    });
  }

  sendContractRequest() {
     this.isSendingRequest = true;

    const httpFormData = new FormData();

    const developerId = String(this.selectedDeveloper.developerId);
    httpFormData.append('developerId', developerId);
    httpFormData.append('brokerId', String(this.brokerId));

    const fileFields = ['image', 'idFront', 'idBack'];

    fileFields.forEach((field) => {
      const files = this.stepForm.get(field)?.value;
      if (files && files.length) {
        httpFormData.append(field, files[0]);
      }
    });

    this.contractService.createContractRequest(httpFormData).subscribe({
      next: async (response) => {
        // Success message
        await Swal.fire({
          title: this.getTranslatedText('SUCCESS'),
          text: this.getTranslatedText('CONTRACT_REQUEST_SUCCESS'),
          icon: 'success',
          confirmButtonText: this.getTranslatedText('OK')
        });

        this.stepForm.reset({
          image: [],
          idFront: [],
          idBack: [],
        });
        this.modalService.dismissAll();
        this.reloadTable(this.page);

        // Stop loading
        this.isSendingRequest = false;
      },
      error: (err) => {
        console.error('Error sending contract request:', err);

        // Format error message
        let errorMessage = 'An error occurred while sending the contract request.';

        if (err.error) {
          if (typeof err.error === 'string') {
            errorMessage = err.error;
          } else if (err.error.message) {
            errorMessage = err.error.message;
          } else if (err.error.errors) {
            // Handle validation errors
            const errors = Object.values(err.error.errors);
            errorMessage = errors.join('\n');
          }
        } else if (err.message) {
          errorMessage = err.message;
        }

        // Show error message
        Swal.fire({
          title: this.getTranslatedText('ERROR'),
          text: this.getTranslatedText('CONTRACT_REQUEST_ERROR'),
          icon: 'error',
          confirmButtonText: this.getTranslatedText('OK')
        }).then(() => {
          // Stop loading when user clicks OK
          this.isSendingRequest = false;
        });
      },
    });
  }

  getDeveloperStatus(brokers: any[]): string {
    const isContracted = brokers.some(
      (broker) => broker.brokerId === this.brokerId
    );
    return isContracted ? this.getTranslatedText('CONTRACTED') : this.getTranslatedText('SEND_CONTRACT_REQUEST');
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'DEVELOPERS': 'المطورين',
        'ALL_DEVELOPERS': 'جميع المطورين',
        'CONTRACTED': 'متعاقد',
        'NOT_CONTRACTED': 'غير متعاقد',
        'SEND_CONTRACT_REQUEST': 'إرسال طلب عقد',
        'SEARCH_DEVELOPERS': 'البحث في المطورين...',
        'DEVELOPER_NAME': 'اسم المطور',
        'PHONE': 'الهاتف',
        'EMAIL': 'البريد الإلكتروني',
        'CITY': 'المدينة',
        'AREA': 'المنطقة',
        'STATUS': 'الحالة',
        'ACTIONS': 'الإجراءات',
        'CONTRACT_REQUEST': 'طلب عقد',
        'UPLOAD_DOCUMENTS': 'رفع المستندات',
        'PERSONAL_IMAGE': 'الصورة الشخصية',
        'ID_FRONT': 'صورة البطاقة الأمامية',
        'ID_BACK': 'صورة البطاقة الخلفية',
        'UPLOAD_FILES': 'رفع الملفات',
        'SEND_REQUEST': 'إرسال الطلب',
        'CANCEL': 'إلغاء',
        'SUCCESS': 'نجح!',
        'ERROR': 'خطأ!',
        'CONTRACT_REQUEST_SUCCESS': 'تم إرسال طلب العقد بنجاح!',
        'CONTRACT_REQUEST_ERROR': 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.',
        'FAILED_TO_LOAD_DATA': 'فشل في تحميل البيانات. يرجى المحاولة مرة أخرى لاحقاً.',
        'OK': 'موافق',
        'FILES_UPLOADED': 'ملف مرفوع',
        'NO_FILES': 'لا توجد ملفات',
        'CONTRACT_DURATION': 'مدة العقد',
        'CONTRACT_DATE': 'تاريخ العقد',
        'CONTRACT_END_DATE': 'تاريخ انتهاء العقد',
        'PROJECTS': 'مشاريع',
        'NO_OF_PROJECTS': 'عدد المشاريع',
        'FILTER': 'تصفية',
        'ADD_DEVELOPER': 'إضافة مطور',
        'VIEW_DEVELOPER': 'عرض المطور',
        'SEND_CONTRACT_REQUEST_MODAL': 'إرسال طلب عقد',
        'UPLOAD_DOCUMENTS_DESC': 'يرجى رفع المستندات المطلوبة',
        'PROFILE_PICTURE': 'الصورة الشخصية',
        'NATIONAL_ID_FRONT': 'صورة البطاقة الأمامية',
        'NATIONAL_ID_BACK': 'صورة البطاقة الخلفية',
        'FILE_SIZE': 'حجم الملف: حتى 5 ميجابايت'
      },
      'en': {
        'DEVELOPERS': 'Developers',
        'ALL_DEVELOPERS': 'All Developers',
        'CONTRACTED': 'Contracted',
        'NOT_CONTRACTED': 'Not Contracted',
        'SEND_CONTRACT_REQUEST': 'Send Contract Request',
        'SEARCH_DEVELOPERS': 'Search developers...',
        'DEVELOPER_NAME': 'Developer Name',
        'PHONE': 'Phone',
        'EMAIL': 'Email',
        'CITY': 'City',
        'AREA': 'Area',
        'STATUS': 'Status',
        'ACTIONS': 'Actions',
        'CONTRACT_REQUEST': 'Contract Request',
        'UPLOAD_DOCUMENTS': 'Upload Documents',
        'PERSONAL_IMAGE': 'Personal Image',
        'ID_FRONT': 'ID Front',
        'ID_BACK': 'ID Back',
        'UPLOAD_FILES': 'Upload Files',
        'SEND_REQUEST': 'Send Request',
        'CANCEL': 'Cancel',
        'SUCCESS': 'Success!',
        'ERROR': 'Error!',
        'CONTRACT_REQUEST_SUCCESS': 'Contract request submitted successfully!',
        'CONTRACT_REQUEST_ERROR': 'An error occurred while sending the contract request.',
        'FAILED_TO_LOAD_DATA': 'Failed to load data. Please try again later.',
        'OK': 'OK',
        'FILES_UPLOADED': 'file(s) uploaded',
        'NO_FILES': 'No files',
        'CONTRACT_DURATION': 'Contract Duration',
        'CONTRACT_DATE': 'Contract Date',
        'CONTRACT_END_DATE': 'Contract End Date',
        'PROJECTS': 'projects',
        'NO_OF_PROJECTS': 'Number of Projects',
        'FILTER': 'Filter',
        'ADD_DEVELOPER': 'Add Developer',
        'VIEW_DEVELOPER': 'View Developer',
        'SEND_CONTRACT_REQUEST_MODAL': 'Send Contract Request',
        'UPLOAD_DOCUMENTS_DESC': 'Please upload the required documents',
        'PROFILE_PICTURE': 'Profile Picture',
        'NATIONAL_ID_FRONT': 'National ID Front',
        'NATIONAL_ID_BACK': 'National ID Back',
        'FILE_SIZE': 'File size: up to 5MB'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  setActiveTab(tab: 'all' | 'contracted' | 'not-contracted') {
    console.log(tab);
    this.activeTab = tab;
    this.reloadTable(this.page);
  }

  private setupSearchDebounce() {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.searchInput = searchTerm;
      this.page.pageNumber = 0;
      this.reloadTable(this.page);
    });
  }

  onSearch(event: any) {
    this.searchInput = event.target.value;
    this.page.pageNumber = 0;
    this.page.filters = { search:this.searchInput};
    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    const params = {
      pageNumber: this.page.pageNumber,
      size: this.page.size,
      orderBy: this.orderBy,
      orderDir: this.orderDir,
    };

    this.page.size = environment.TABLE_LIMIT;
    this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
    this.loading = true;
    console.log('Request params:', params);

    await this._service.getAll(this.page).subscribe(
      (response: any) => {
        console.log('Response data:', response.data);
        let data = response.data;
        this.rows = Array.isArray(response.data) ? response.data : [];
        this.rows = [...this.rows];

        if (this.activeTab === 'contracted') {
          this.rows = data.filter(
            (row: any) => this.getDeveloperStatus(row.brokers) === 'contracted'
          );
        } else if (this.activeTab === 'not-contracted') {
          this.rows = data.filter(
            (row: any) => this.getDeveloperStatus(row.brokers) === 'send contract request'
          );
        }

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.error('Error loading data:', error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire(
          this.getTranslatedText('FAILED_TO_LOAD_DATA'),
          '',
          'error'
        );
      }
    );
  }
}
