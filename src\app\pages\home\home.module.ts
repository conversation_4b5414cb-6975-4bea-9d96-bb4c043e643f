import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { HomeComponent } from './home.component';
import { AllPropertiesComponent } from './All Properties/all-properties.component';
import { UnitFilterComponent } from './All Properties/components/unit-filter/unit-filter.component';
import { SharedModule } from '../../_metronic/shared/shared.module';
import { TranslationModule } from '../../modules/i18n/translation.module';
import { LanguageToggleComponent } from '../shared/language-toggle/language-toggle.component';
import { PropertyService } from '../broker/services/property.service';
import { PropertyTranslationService } from '../../shared/services/property-translation.service';

@NgModule({
  declarations: [
    HomeComponent,
    AllPropertiesComponent,
    UnitFilterComponent,
    LanguageToggleComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    HttpClientModule,
    SharedModule,
    TranslationModule,
    RouterModule.forChild([
      {
        path: '',
        component: HomeComponent
      },
      {
        path: 'properties',
        component: AllPropertiesComponent
      }
    ])
  ],
  providers: [
    PropertyService,
    PropertyTranslationService
  ]
})
export class HomeModule { }
