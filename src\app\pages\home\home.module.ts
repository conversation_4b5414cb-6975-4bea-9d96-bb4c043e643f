import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HomeComponent } from './home.component';
import { AllPropertiesComponent } from './All Properties/all-properties.component';
import { UnitFilterComponent } from './All Properties/components/unit-filter/unit-filter.component';
import { SharedModule } from '../../_metronic/shared/shared.module';
import { TranslationModule } from '../../modules/i18n/translation.module';
import { LanguageToggleComponent } from '../shared/language-toggle/language-toggle.component';

@NgModule({
  declarations: [
    HomeComponent,
    AllPropertiesComponent,
    UnitFilterComponent,
    LanguageToggleComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    TranslationModule,
    RouterModule.forChild([
      {
        path: '',
        component: HomeComponent
      },
      {
        path: 'properties',
        component: AllPropertiesComponent
      }
    ])
  ]
})
export class HomeModule { }
